import React from 'react';

interface GlowingOrbProps {
  isListening: boolean;
  isSpeaking: boolean;
  className?: string;
}

const GlowingOrb: React.FC<GlowingOrbProps> = ({ 
  isListening, 
  isSpeaking, 
  className = "" 
}) => {
  const getOrbState = () => {
    if (isSpeaking) return 'orb-speaking';
    if (isListening) return 'orb-listening';
    return 'orb-idle';
  };

  return (
    <div className={`relative flex items-center justify-center ${className}`}>
      {/* Main Orb */}
      <div 
        className={`
          w-32 h-32 rounded-full 
          bg-gradient-to-r from-red-500 via-red-600 to-red-700
          ${getOrbState()}
          transition-all duration-300 ease-in-out
          relative z-10
        `}
      >
        {/* Inner glow layers */}
        <div className="absolute inset-2 rounded-full bg-gradient-to-r from-red-400 to-red-500 opacity-80" />
        <div className="absolute inset-4 rounded-full bg-gradient-to-r from-red-300 to-red-400 opacity-60" />
        <div className="absolute inset-6 rounded-full bg-gradient-to-r from-red-200 to-red-300 opacity-40" />
        <div className="absolute inset-8 rounded-full bg-white opacity-20" />
        
        {/* Core light */}
        <div className="absolute inset-10 rounded-full bg-white opacity-60 blur-sm" />
        <div className="absolute inset-12 rounded-full bg-white opacity-80 blur-xs" />
      </div>
      
      {/* Outer glow rings */}
      <div className="absolute inset-0 rounded-full bg-red-500 opacity-20 blur-xl animate-pulse" />
      <div className="absolute inset-[-10px] rounded-full bg-red-400 opacity-15 blur-2xl" />
      <div className="absolute inset-[-20px] rounded-full bg-red-300 opacity-10 blur-3xl" />
      
      {/* Particle effects when speaking */}
      {isSpeaking && (
        <>
          <div className="absolute w-2 h-2 bg-red-300 rounded-full top-4 left-8 animate-ping opacity-60" />
          <div className="absolute w-1 h-1 bg-blue-300 rounded-full top-8 right-6 animate-ping opacity-70 animation-delay-200" />
          <div className="absolute w-1.5 h-1.5 bg-purple-300 rounded-full bottom-6 left-12 animate-ping opacity-50 animation-delay-400" />
          <div className="absolute w-1 h-1 bg-yellow-300 rounded-full bottom-8 right-8 animate-ping opacity-60 animation-delay-600" />
        </>
      )}
      
      {/* Listening indicator */}
      {isListening && (
        <div className="absolute inset-0 rounded-full border-2 border-blue-400 animate-ping opacity-60" />
      )}
    </div>
  );
};

export default GlowingOrb;
