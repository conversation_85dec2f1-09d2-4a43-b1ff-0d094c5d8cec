import React, { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Mic, Book, ArrowUp } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import VoiceInput from '@/components/VoiceInput';
import ChatMessage from '@/components/ChatMessage';
import ScriptureCard from '@/components/ScriptureCard';

interface Message {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  scripture?: ScripturePassage[];
}

interface ScripturePassage {
  faith: string;
  book: string;
  chapter: number;
  verse: string;
  text: string;
  citation: string;
}

const Index = () => {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      role: 'assistant',
      content: 'Peace be with you. I am <PERSON><PERSON>, your interfaith scripture companion. Ask me about guidance from the world\'s sacred texts, and I will share wisdom from different traditions to illuminate your path.',
      timestamp: new Date(),
    }
  ]);
  const [inputText, setInputText] = useState('');
  const [isListening, setIsListening] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const { toast } = useToast();

  // Mock scripture database for demo
  const mockScriptures: ScripturePassage[] = [
    {
      faith: 'Christianity',
      book: 'Matthew',
      chapter: 7,
      verse: '7',
      text: 'Ask and it will be given to you; seek and you will find; knock and the door will be opened to you.',
      citation: 'Christianity - Matthew 7:7'
    },
    {
      faith: 'Islam',
      book: 'Quran',
      chapter: 2,
      verse: '186',
      text: 'And when My servants ask you concerning Me, indeed I am near. I respond to the invocation of the supplicant when he calls upon Me.',
      citation: 'Islam - Quran 2:186'
    },
    {
      faith: 'Buddhism',
      book: 'Dhammapada',
      chapter: 1,
      verse: '1',
      text: 'All that we are is the result of what we have thought. The mind is everything. What we think we become.',
      citation: 'Buddhism - Dhammapada 1:1'
    },
    {
      faith: 'Hinduism',
      book: 'Bhagavad Gita',
      chapter: 2,
      verse: '47',
      text: 'You have a right to perform your prescribed duty, but not to the fruits of actions. Never consider yourself the cause of the results of your activities.',
      citation: 'Hinduism - Bhagavad Gita 2:47'
    }
  ];

  useEffect(() => {
    if (scrollAreaRef.current) {
      scrollAreaRef.current.scrollTop = scrollAreaRef.current.scrollHeight;
    }
  }, [messages]);

  const generateResponse = (userQuestion: string): { reflection: string; scriptures: ScripturePassage[] } => {
    const question = userQuestion.toLowerCase();

    // Simple keyword-based response system
    if (question.includes('psalm') || question.includes('psalms')) {
      if (question.includes('119')) {
        return {
          reflection: `Psalm 119 is the longest chapter in the Bible, a magnificent meditation on God's Word and Law. It's an acrostic poem with 176 verses, each section beginning with a successive letter of the Hebrew alphabet. The psalm celebrates the beauty, power, and life-giving nature of God's teachings. It speaks of finding guidance, comfort, and strength through divine wisdom. The psalmist expresses deep love for God's law, seeing it not as burden but as a lamp to guide one's path and a source of joy and meditation.`,
          scriptures: [
            {
              faith: 'Christianity',
              book: 'Psalm',
              chapter: 119,
              verse: '105',
              text: 'Your word is a lamp for my feet, a light on my path.',
              citation: 'Christianity - Psalm 119:105'
            },
            {
              faith: 'Christianity',
              book: 'Psalm',
              chapter: 119,
              verse: '11',
              text: 'I have hidden your word in my heart that I might not sin against you.',
              citation: 'Christianity - Psalm 119:11'
            },
            {
              faith: 'Christianity',
              book: 'Psalm',
              chapter: 119,
              verse: '97',
              text: 'Oh, how I love your law! I meditate on it all day long.',
              citation: 'Christianity - Psalm 119:97'
            }
          ]
        };
      } else {
        return {
          reflection: `The Psalms are a collection of 150 sacred songs and prayers that express the full range of human emotion and experience before God. They offer comfort in sorrow, praise in joy, and guidance in uncertainty. Many traditions find wisdom in these ancient texts about faith, trust, and the human relationship with the divine.`,
          scriptures: [
            {
              faith: 'Christianity',
              book: 'Psalm',
              chapter: 23,
              verse: '1',
              text: 'The Lord is my shepherd, I lack nothing.',
              citation: 'Christianity - Psalm 23:1'
            },
            {
              faith: 'Christianity',
              book: 'Psalm',
              chapter: 46,
              verse: '10',
              text: 'Be still, and know that I am God.',
              citation: 'Christianity - Psalm 46:10'
            }
          ]
        };
      }
    }

    if (question.includes('love') || question.includes('compassion')) {
      return {
        reflection: `Love and compassion are central themes across all sacred traditions. They teach us that divine love is unconditional and that we are called to extend this same love to others. Whether through Christian agape, Islamic rahma, Buddhist metta, or Hindu bhakti, all traditions recognize love as the highest spiritual principle.`,
        scriptures: [
          {
            faith: 'Christianity',
            book: '1 John',
            chapter: 4,
            verse: '8',
            text: 'Whoever does not love does not know God, because God is love.',
            citation: 'Christianity - 1 John 4:8'
          },
          {
            faith: 'Islam',
            book: 'Quran',
            chapter: 1,
            verse: '1',
            text: 'In the name of Allah, the Most Gracious, the Most Merciful.',
            citation: 'Islam - Quran 1:1'
          },
          {
            faith: 'Buddhism',
            book: 'Metta Sutta',
            chapter: 1,
            verse: '1',
            text: 'May all beings be happy and secure, may their hearts be wholesome.',
            citation: 'Buddhism - Metta Sutta'
          }
        ]
      };
    }

    if (question.includes('peace') || question.includes('calm') || question.includes('anxiety') || question.includes('worry')) {
      return {
        reflection: `Peace is a gift that transcends understanding, available to all who seek it. Sacred texts teach us that true peace comes not from external circumstances but from inner alignment with divine will. Through prayer, meditation, and trust, we can find calm even in life's storms.`,
        scriptures: [
          {
            faith: 'Christianity',
            book: 'Philippians',
            chapter: 4,
            verse: '7',
            text: 'And the peace of God, which transcends all understanding, will guard your hearts and your minds in Christ Jesus.',
            citation: 'Christianity - Philippians 4:7'
          },
          {
            faith: 'Islam',
            book: 'Quran',
            chapter: 13,
            verse: '28',
            text: 'Verily, in the remembrance of Allah do hearts find rest.',
            citation: 'Islam - Quran 13:28'
          },
          {
            faith: 'Buddhism',
            book: 'Dhammapada',
            chapter: 1,
            verse: '2',
            text: 'Peace comes from within. Do not seek it without.',
            citation: 'Buddhism - Dhammapada 1:2'
          }
        ]
      };
    }

    if (question.includes('wisdom') || question.includes('guidance') || question.includes('decision')) {
      return {
        reflection: `Wisdom is the application of divine knowledge to human life. All sacred traditions teach that true wisdom comes from seeking divine guidance through prayer, study, and contemplation. When we align our hearts with divine will, we receive the insight needed for righteous living.`,
        scriptures: [
          {
            faith: 'Christianity',
            book: 'Proverbs',
            chapter: 3,
            verse: '5-6',
            text: 'Trust in the Lord with all your heart and lean not on your own understanding; in all your ways submit to him, and he will make your paths straight.',
            citation: 'Christianity - Proverbs 3:5-6'
          },
          {
            faith: 'Islam',
            book: 'Quran',
            chapter: 2,
            verse: '269',
            text: 'He gives wisdom to whom He wills, and whoever has been given wisdom has certainly been given much good.',
            citation: 'Islam - Quran 2:269'
          },
          {
            faith: 'Hinduism',
            book: 'Bhagavad Gita',
            chapter: 18,
            verse: '66',
            text: 'Abandon all varieties of religion and just surrender unto Me. I shall deliver you from all sinful reactions. Do not fear.',
            citation: 'Hinduism - Bhagavad Gita 18:66'
          }
        ]
      };
    }

    // Default response for other questions
    return {
      reflection: `Your question touches on profound spiritual themes that resonate across many faith traditions. While each tradition has its unique perspective, they often share common wisdom about the human search for meaning, purpose, and connection with the divine. Let me share some relevant passages that might offer insight.`,
      scriptures: mockScriptures.slice(0, 3)
    };
  };

  const handleSendMessage = async (text: string) => {
    if (!text.trim()) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      role: 'user',
      content: text,
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    setInputText('');
    setIsLoading(true);

    // Simulate API delay and generate contextual response
    setTimeout(() => {
      const { reflection, scriptures } = generateResponse(text);

      const assistantMessage: Message = {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: reflection,
        timestamp: new Date(),
        scripture: scriptures,
      };

      setMessages(prev => [...prev, assistantMessage]);
      setIsLoading(false);

      // Text-to-speech for the response
      if ('speechSynthesis' in window) {
        const utterance = new SpeechSynthesisUtterance(reflection);
        utterance.rate = 0.9;
        utterance.pitch = 1;
        speechSynthesis.speak(utterance);
      }
    }, 1500);
  };

  const handleVoiceInput = (transcript: string) => {
    handleSendMessage(transcript);
  };

  const handleVoiceError = (error: string) => {
    toast({
      title: 'Voice Recognition Error',
      description: error,
      variant: 'destructive',
    });
  };

  return (
    <div className="min-h-screen bg-gradient-wisdom">
      {/* Header */}
      <header className="border-b border-border bg-card/50 backdrop-blur-sm">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-gradient-sacred">
              <Book className="h-6 w-6 text-primary-foreground" />
            </div>
            <div>
              <h1 className="text-2xl font-semibold text-foreground font-interface">
                Logos
              </h1>
              <p className="text-sm text-muted-foreground">
                Interfaith Scripture Companion
              </p>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-4 py-6 max-w-4xl">
        <div className="flex flex-col h-[calc(100vh-200px)]">
          {/* Chat Messages */}
          <ScrollArea className="flex-1 mb-6 chat-scroll" ref={scrollAreaRef}>
            <div className="space-y-6 pr-4">
              {messages.map((message) => (
                <ChatMessage
                  key={message.id}
                  message={message}
                  scriptures={message.scripture}
                />
              ))}
              {isLoading && (
                <div className="flex justify-start">
                  <Card className="p-4 max-w-2xl shadow-wisdom">
                    <div className="flex items-center gap-2 text-muted-foreground">
                      <div className="w-2 h-2 bg-accent rounded-full animate-pulse"></div>
                      <div className="w-2 h-2 bg-accent rounded-full animate-pulse delay-75"></div>
                      <div className="w-2 h-2 bg-accent rounded-full animate-pulse delay-150"></div>
                      <span className="ml-2">Seeking wisdom...</span>
                    </div>
                  </Card>
                </div>
              )}
            </div>
          </ScrollArea>

          {/* Input Area */}
          <div className="border-t border-border pt-4">
            <Card className="p-4 shadow-sacred">
              <div className="flex gap-3 items-end">
                <div className="flex-1">
                  <Input
                    value={inputText}
                    onChange={(e) => setInputText(e.target.value)}
                    placeholder="Ask about wisdom from sacred texts... or use voice"
                    className="resize-none border-muted-foreground/20 focus:border-accent"
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        handleSendMessage(inputText);
                      }
                    }}
                  />
                </div>
                
                <VoiceInput
                  onTranscript={handleVoiceInput}
                  onError={handleVoiceError}
                  isListening={isListening}
                  setIsListening={setIsListening}
                />

                <Button
                  onClick={() => handleSendMessage(inputText)}
                  disabled={!inputText.trim() || isLoading}
                  className="bg-gradient-sacred hover:shadow-voice transition-all duration-300"
                  size="icon"
                >
                  <ArrowUp className="h-4 w-4" />
                </Button>
              </div>
            </Card>
          </div>
        </div>
      </main>
    </div>
  );
};

export default Index;