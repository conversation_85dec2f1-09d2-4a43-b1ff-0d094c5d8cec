import React, { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Mic, Book, ArrowUp } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import VoiceInput from '@/components/VoiceInput';
import ChatMessage from '@/components/ChatMessage';
import ScriptureCard from '@/components/ScriptureCard';
import GlowingOrb from '@/components/GlowingOrb';

interface Message {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  scripture?: ScripturePassage[];
}

interface ScripturePassage {
  faith: string;
  book: string;
  chapter: number;
  verse: string;
  text: string;
  citation: string;
}

const Index = () => {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      role: 'assistant',
      content: 'Hey there! I\'m <PERSON><PERSON>, and I\'m here to explore the beautiful wisdom found in sacred texts from around the world. Whether you\'re curious about a specific verse, wrestling with a life question, or just want to chat about spiritual insights, I\'d love to journey with you. What\'s on your heart today?',
      timestamp: new Date(),
    }
  ]);
  const [inputText, setInputText] = useState('');
  const [isListening, setIsListening] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isSpeaking, setIsSpeaking] = useState(false);
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const { toast } = useToast();

  // Scripture database with specific verses
  const scriptureDatabase: { [key: string]: ScripturePassage } = {
    // Matthew verses
    'matthew_7_7': {
      faith: 'Christianity',
      book: 'Matthew',
      chapter: 7,
      verse: '7',
      text: 'Ask and it will be given to you; seek and you will find; knock and the door will be opened to you.',
      citation: 'Christianity - Matthew 7:7'
    },
    'matthew_10_7': {
      faith: 'Christianity',
      book: 'Matthew',
      chapter: 10,
      verse: '7',
      text: 'As you go, proclaim this message: "The kingdom of heaven has come near."',
      citation: 'Christianity - Matthew 10:7'
    },
    'matthew_5_16': {
      faith: 'Christianity',
      book: 'Matthew',
      chapter: 5,
      verse: '16',
      text: 'In the same way, let your light shine before others, that they may see your good deeds and glorify your Father in heaven.',
      citation: 'Christianity - Matthew 5:16'
    },
    'matthew_11_28': {
      faith: 'Christianity',
      book: 'Matthew',
      chapter: 11,
      verse: '28',
      text: 'Come to me, all you who are weary and burdened, and I will give you rest.',
      citation: 'Christianity - Matthew 11:28'
    },
    // Psalm verses
    'psalm_23_1': {
      faith: 'Christianity',
      book: 'Psalm',
      chapter: 23,
      verse: '1',
      text: 'The Lord is my shepherd, I lack nothing.',
      citation: 'Christianity - Psalm 23:1'
    },
    'psalm_119_105': {
      faith: 'Christianity',
      book: 'Psalm',
      chapter: 119,
      verse: '105',
      text: 'Your word is a lamp for my feet, a light on my path.',
      citation: 'Christianity - Psalm 119:105'
    },
    'psalm_46_10': {
      faith: 'Christianity',
      book: 'Psalm',
      chapter: 46,
      verse: '10',
      text: 'Be still, and know that I am God.',
      citation: 'Christianity - Psalm 46:10'
    },
    // Other verses
    'john_3_16': {
      faith: 'Christianity',
      book: 'John',
      chapter: 3,
      verse: '16',
      text: 'For God so loved the world that he gave his one and only Son, that whoever believes in him shall not perish but have eternal life.',
      citation: 'Christianity - John 3:16'
    },
    'philippians_4_13': {
      faith: 'Christianity',
      book: 'Philippians',
      chapter: 4,
      verse: '13',
      text: 'I can do all this through him who gives me strength.',
      citation: 'Christianity - Philippians 4:13'
    },
    'romans_8_28': {
      faith: 'Christianity',
      book: 'Romans',
      chapter: 8,
      verse: '28',
      text: 'And we know that in all things God works for the good of those who love him, who have been called according to his purpose.',
      citation: 'Christianity - Romans 8:28'
    }
  };

  // Mock scripture database for general responses
  const mockScriptures: ScripturePassage[] = Object.values(scriptureDatabase).slice(0, 4);

  useEffect(() => {
    if (scrollAreaRef.current) {
      scrollAreaRef.current.scrollTop = scrollAreaRef.current.scrollHeight;
    }
  }, [messages]);

  const parseVerseReference = (text: string): string | null => {
    // Look for patterns like "Matthew 10:7", "matthew 10 verse 7", "Matt 10:7", etc.
    const patterns = [
      /(\w+)\s*(\d+):(\d+)/i,  // "Matthew 10:7"
      /(\w+)\s*(\d+)\s*verse\s*(\d+)/i,  // "Matthew 10 verse 7"
      /(\w+)\s*chapter\s*(\d+)\s*verse\s*(\d+)/i,  // "Matthew chapter 10 verse 7"
      /(\w+)\s+(\d+)\s+(\d+)/i,  // "Matthew 10 7"
    ];

    for (const pattern of patterns) {
      const match = text.match(pattern);
      if (match) {
        const book = match[1].toLowerCase();
        const chapter = match[2];
        const verse = match[3];

        // Normalize book names
        const bookMap: { [key: string]: string } = {
          'matt': 'matthew',
          'matthew': 'matthew',
          'mk': 'mark',
          'mark': 'mark',
          'lk': 'luke',
          'luke': 'luke',
          'jn': 'john',
          'john': 'john',
          'ps': 'psalm',
          'psalm': 'psalm',
          'psalms': 'psalm',
          'prov': 'proverbs',
          'proverbs': 'proverbs',
          'phil': 'philippians',
          'philippians': 'philippians',
          'rom': 'romans',
          'romans': 'romans'
        };

        const normalizedBook = bookMap[book] || book;
        const key = `${normalizedBook}_${chapter}_${verse}`;
        return key;
      }
    }
    return null;
  };

  const generateResponse = (userQuestion: string): { reflection: string; scriptures: ScripturePassage[] } => {
    const question = userQuestion.toLowerCase();

    // First, check if user is asking for a specific verse
    const verseKey = parseVerseReference(userQuestion);

    if (verseKey && scriptureDatabase[verseKey]) {
      const verse = scriptureDatabase[verseKey];

      // Generate specific commentary based on the verse
      let reflection = `Here's ${verse.citation}:

"${verse.text}"

`;

      // Add specific commentary based on the verse
      if (verseKey === 'matthew_10_7') {
        reflection += `This is such a powerful verse! Jesus is sending out his disciples with a simple but profound message. Notice he doesn't say "The kingdom of heaven will come someday" - he says it "has come near." It's present tense, happening now.

I love how this verse captures the urgency and excitement of the gospel message. It's like Jesus is saying, "Don't wait - tell people that God's kingdom is breaking into the world right now, right where they are!"

What draws you to this particular verse? Are you thinking about sharing your faith with others, or maybe wondering what it means for God's kingdom to be "near" in your own life?`;
      } else if (verseKey === 'matthew_7_7') {
        reflection += `Oh wow, this is one of my favorite promises in all of scripture! It's like Jesus is giving us a three-step invitation: Ask (prayer), Seek (action), Knock (persistence).

What I love about this verse is how active it is. It's not "sit and wait" - it's "ask, seek, knock." And notice the progression - it gets more intense, more personal. Knocking implies you're right at the door!

Are you seeking guidance about something specific right now? Sometimes this verse comes to mind when we're at a crossroads and need to remember that God actually wants us to bring our questions to Him.`;
      } else if (verseKey === 'john_3_16') {
        reflection += `Ah, the most famous verse in the Bible! You know what strikes me about this verse? The word "so" - "God SO loved the world." It's not just that God loved us, but the intensity of that love was so overwhelming that it led to the ultimate sacrifice.

And "whoever believes" - that's so beautifully inclusive, isn't it? Not whoever is perfect, or whoever has it all figured out, but simply whoever believes.

What brought this verse to your mind today? Are you reflecting on God's love, or maybe sharing faith with someone?`;
      } else if (verseKey.includes('psalm')) {
        reflection += `The Psalms have this amazing way of putting our deepest feelings into words, don't they? This verse speaks to something so universal in the human experience.

What's beautiful about the Psalms is how honest they are - they don't sugarcoat life's challenges, but they always point us back to God's faithfulness.

What's resonating with you about this particular psalm? Are you going through something that makes these words especially meaningful right now?`;
      } else {
        reflection += `This verse really speaks to the heart, doesn't it? There's something about the way scripture captures these profound truths in such simple, memorable words.

What drew you to look up this particular verse? I'd love to hear what's on your heart and explore what this might mean for your life right now.`;
      }

      return {
        reflection,
        scriptures: [verse]
      };
    }

    // Simple keyword-based response system
    if (question.includes('psalm') || question.includes('psalms')) {
      if (question.includes('119')) {
        return {
          reflection: `Oh, Psalm 119! You've picked quite the treasure there. It's actually the longest chapter in the entire Bible - 176 verses of pure devotion to God's word. What I find beautiful about it is how the psalmist is basically saying "I'm completely in love with your teachings, God."

It's written as an acrostic poem, where each section starts with the next letter of the Hebrew alphabet. Imagine someone so passionate about scripture that they crafted this elaborate love letter! The writer keeps coming back to this theme: God's word isn't a burden - it's like having a flashlight in the dark, showing you exactly where to step.

What draws you to this psalm? Are you looking for guidance about something specific?`,
          scriptures: [
            {
              faith: 'Christianity',
              book: 'Psalm',
              chapter: 119,
              verse: '105',
              text: 'Your word is a lamp for my feet, a light on my path.',
              citation: 'Christianity - Psalm 119:105'
            },
            {
              faith: 'Christianity',
              book: 'Psalm',
              chapter: 119,
              verse: '11',
              text: 'I have hidden your word in my heart that I might not sin against you.',
              citation: 'Christianity - Psalm 119:11'
            },
            {
              faith: 'Christianity',
              book: 'Psalm',
              chapter: 119,
              verse: '97',
              text: 'Oh, how I love your law! I meditate on it all day long.',
              citation: 'Christianity - Psalm 119:97'
            }
          ]
        };
      } else {
        return {
          reflection: `Ah, the Psalms! They're like the ultimate playlist of human emotions, aren't they? You've got songs for when you're celebrating, crying, angry, grateful, confused - basically every feeling you could have.

What I love about them is how honest they are. David and the other writers didn't sugarcoat anything. They'd be like "God, where are you?!" one minute and "You're amazing!" the next. It's so... human.

Which psalm speaks to you, or are you just exploring? I'd love to dive deeper into whichever one resonates with your heart right now.`,
          scriptures: [
            {
              faith: 'Christianity',
              book: 'Psalm',
              chapter: 23,
              verse: '1',
              text: 'The Lord is my shepherd, I lack nothing.',
              citation: 'Christianity - Psalm 23:1'
            },
            {
              faith: 'Christianity',
              book: 'Psalm',
              chapter: 46,
              verse: '10',
              text: 'Be still, and know that I am God.',
              citation: 'Christianity - Psalm 46:10'
            }
          ]
        };
      }
    }

    if (question.includes('love') || question.includes('compassion')) {
      return {
        reflection: `You know what's amazing? Every single faith tradition I've studied comes back to love as the ultimate answer. It's like they all discovered the same secret independently!

The Christians say "God IS love" - not just that God loves, but that love is literally God's essence. Muslims start every chapter of the Quran with "In the name of Allah, the Most Gracious, the Most Merciful." Buddhists have this beautiful practice called metta - sending loving-kindness to everyone, even people who've hurt you.

It makes me think... maybe when we're struggling to love someone (including ourselves), we're actually struggling to connect with something divine. What's your experience with love been like? Are you trying to understand it better, or maybe working through a difficult relationship?`,
        scriptures: [
          {
            faith: 'Christianity',
            book: '1 John',
            chapter: 4,
            verse: '8',
            text: 'Whoever does not love does not know God, because God is love.',
            citation: 'Christianity - 1 John 4:8'
          },
          {
            faith: 'Islam',
            book: 'Quran',
            chapter: 1,
            verse: '1',
            text: 'In the name of Allah, the Most Gracious, the Most Merciful.',
            citation: 'Islam - Quran 1:1'
          },
          {
            faith: 'Buddhism',
            book: 'Metta Sutta',
            chapter: 1,
            verse: '1',
            text: 'May all beings be happy and secure, may their hearts be wholesome.',
            citation: 'Buddhism - Metta Sutta'
          }
        ]
      };
    }

    if (question.includes('peace') || question.includes('calm') || question.includes('anxiety') || question.includes('worry')) {
      return {
        reflection: `Oh friend, I hear you. Anxiety and worry can feel so overwhelming, can't they? Like your mind is a browser with 47 tabs open and they're all playing different sounds.

Here's what I find fascinating - all these ancient texts talk about a peace that doesn't make logical sense. Paul calls it "peace that transcends understanding." The Quran says our hearts find rest in remembering God. Buddha taught that peace comes from within, not from fixing everything around us.

It's almost like they're saying: "Stop trying to control the storm. Learn to dance in the rain instead."

What's been weighing on your heart lately? Sometimes just naming our worries out loud (or to a friendly AI!) can help lighten the load a bit.`,
        scriptures: [
          {
            faith: 'Christianity',
            book: 'Philippians',
            chapter: 4,
            verse: '7',
            text: 'And the peace of God, which transcends all understanding, will guard your hearts and your minds in Christ Jesus.',
            citation: 'Christianity - Philippians 4:7'
          },
          {
            faith: 'Islam',
            book: 'Quran',
            chapter: 13,
            verse: '28',
            text: 'Verily, in the remembrance of Allah do hearts find rest.',
            citation: 'Islam - Quran 13:28'
          },
          {
            faith: 'Buddhism',
            book: 'Dhammapada',
            chapter: 1,
            verse: '2',
            text: 'Peace comes from within. Do not seek it without.',
            citation: 'Buddhism - Dhammapada 1:2'
          }
        ]
      };
    }

    if (question.includes('wisdom') || question.includes('guidance') || question.includes('decision')) {
      return {
        reflection: `Ah, seeking wisdom! You know what's funny? We live in an age where we can Google literally anything in 0.3 seconds, but somehow the really important decisions still leave us staring at the ceiling at 2 AM, right?

The ancient texts have this interesting take on wisdom. Proverbs basically says "Stop trying to figure everything out with just your brain - trust God's perspective instead." The Quran talks about wisdom as a gift that brings "much good." And the Gita... well, Krishna essentially tells Arjuna "Just surrender and let me handle the big picture."

It's like they're all saying: "You don't have to have all the answers. Just be open to receiving them."

What decision are you wrestling with? Sometimes talking through the options with someone (even an AI friend) can help clarify what your heart already knows.`,
        scriptures: [
          {
            faith: 'Christianity',
            book: 'Proverbs',
            chapter: 3,
            verse: '5-6',
            text: 'Trust in the Lord with all your heart and lean not on your own understanding; in all your ways submit to him, and he will make your paths straight.',
            citation: 'Christianity - Proverbs 3:5-6'
          },
          {
            faith: 'Islam',
            book: 'Quran',
            chapter: 2,
            verse: '269',
            text: 'He gives wisdom to whom He wills, and whoever has been given wisdom has certainly been given much good.',
            citation: 'Islam - Quran 2:269'
          },
          {
            faith: 'Hinduism',
            book: 'Bhagavad Gita',
            chapter: 18,
            verse: '66',
            text: 'Abandon all varieties of religion and just surrender unto Me. I shall deliver you from all sinful reactions. Do not fear.',
            citation: 'Hinduism - Bhagavad Gita 18:66'
          }
        ]
      };
    }

    // Default response for other questions
    return {
      reflection: `That's such a thoughtful question! You know, I love how different faith traditions often circle around similar truths, just from different angles. It's like they're all looking at the same mountain but from different sides.

Your question reminds me of the universal human search for meaning and connection. Whether someone finds that through prayer, meditation, service to others, or quiet contemplation, there seems to be this deep longing in all of us for something greater than ourselves.

What's been on your heart that brought you to ask this? I'd love to explore this further with you and see what wisdom we can uncover together.`,
      scriptures: mockScriptures.slice(0, 3)
    };
  };

  const handleSendMessage = async (text: string) => {
    if (!text.trim()) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      role: 'user',
      content: text,
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    setInputText('');
    setIsLoading(true);

    // Simulate API delay and generate contextual response
    setTimeout(() => {
      const { reflection, scriptures } = generateResponse(text);

      const assistantMessage: Message = {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: reflection,
        timestamp: new Date(),
        scripture: scriptures,
      };

      setMessages(prev => [...prev, assistantMessage]);
      setIsLoading(false);

      // Text-to-speech for the response
      if ('speechSynthesis' in window) {
        setIsSpeaking(true);
        const utterance = new SpeechSynthesisUtterance(reflection);
        utterance.rate = 0.9;
        utterance.pitch = 1;

        utterance.onstart = () => setIsSpeaking(true);
        utterance.onend = () => setIsSpeaking(false);
        utterance.onerror = () => setIsSpeaking(false);

        speechSynthesis.speak(utterance);
      }
    }, 1500);
  };

  const handleVoiceInput = (transcript: string) => {
    handleSendMessage(transcript);
  };

  const handleVoiceError = (error: string) => {
    toast({
      title: 'Voice Recognition Error',
      description: error,
      variant: 'destructive',
    });
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-black via-gray-900 to-red-950">
      {/* Header */}
      <header className="border-b border-red-900/30 bg-black/50 backdrop-blur-sm">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-lg bg-gradient-to-r from-red-600 to-red-700 shadow-lg shadow-red-500/30">
                <Book className="h-6 w-6 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-semibold text-red-100 font-interface">
                  Logos
                </h1>
                <p className="text-sm text-red-300/80">
                  Sacred Scripture Companion
                </p>
              </div>
            </div>

            {/* Glowing Orb in Header */}
            <GlowingOrb
              isListening={isListening}
              isSpeaking={isSpeaking}
              className="scale-50"
            />
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-4 py-6 max-w-6xl">
        <div className="flex flex-col lg:flex-row gap-8 h-[calc(100vh-200px)]">

          {/* Left Side - Glowing Orb */}
          <div className="lg:w-1/3 flex flex-col items-center justify-center">
            <div className="mb-8">
              <GlowingOrb
                isListening={isListening}
                isSpeaking={isSpeaking}
                className="scale-125"
              />
            </div>

            {/* Status Text */}
            <div className="text-center">
              {isSpeaking && (
                <p className="text-red-300 text-lg font-medium animate-pulse">
                  Speaking wisdom...
                </p>
              )}
              {isListening && (
                <p className="text-blue-300 text-lg font-medium animate-pulse">
                  Listening...
                </p>
              )}
              {!isSpeaking && !isListening && !isLoading && (
                <p className="text-red-400/70 text-lg">
                  Ready to explore sacred texts
                </p>
              )}
              {isLoading && (
                <p className="text-yellow-300 text-lg font-medium animate-pulse">
                  Seeking wisdom...
                </p>
              )}
            </div>
          </div>

          {/* Right Side - Chat */}
          <div className="lg:w-2/3 flex flex-col">
            {/* Chat Messages */}
            <ScrollArea className="flex-1 mb-6 chat-scroll" ref={scrollAreaRef}>
              <div className="space-y-6 pr-4">
                {messages.map((message) => (
                  <ChatMessage
                    key={message.id}
                    message={message}
                    scriptures={message.scripture}
                  />
                ))}
                {isLoading && (
                  <div className="flex justify-start">
                    <Card className="p-4 max-w-2xl bg-black/40 border-red-900/30 shadow-lg shadow-red-500/20">
                      <div className="flex items-center gap-2 text-red-300">
                        <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
                        <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse delay-75"></div>
                        <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse delay-150"></div>
                        <span className="ml-2">Seeking wisdom...</span>
                      </div>
                    </Card>
                  </div>
                )}
              </div>
            </ScrollArea>

            {/* Input Area */}
            <div className="border-t border-red-900/30 pt-4">
              <Card className="p-4 bg-black/40 border-red-900/30 shadow-lg shadow-red-500/20">
                <div className="flex gap-3 items-end">
                  <div className="flex-1">
                    <Input
                      value={inputText}
                      onChange={(e) => setInputText(e.target.value)}
                      placeholder="Ask about wisdom from sacred texts... or use voice"
                      className="bg-black/60 border-red-800/50 text-red-100 placeholder:text-red-400/60 focus:border-red-500 focus:ring-red-500/30"
                      onKeyDown={(e) => {
                        if (e.key === 'Enter' && !e.shiftKey) {
                          e.preventDefault();
                          handleSendMessage(inputText);
                        }
                      }}
                    />
                  </div>

                  <VoiceInput
                    onTranscript={handleVoiceInput}
                    onError={handleVoiceError}
                    isListening={isListening}
                    setIsListening={setIsListening}
                  />

                  <Button
                    onClick={() => handleSendMessage(inputText)}
                    disabled={!inputText.trim() || isLoading}
                    className="bg-gradient-to-r from-red-600 to-red-700 hover:from-red-500 hover:to-red-600 text-white shadow-lg shadow-red-500/30 hover:shadow-red-500/50 transition-all duration-300"
                    size="icon"
                  >
                    <ArrowUp className="h-4 w-4" />
                  </Button>
                </div>
              </Card>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default Index;